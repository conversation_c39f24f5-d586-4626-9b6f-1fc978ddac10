"""
Base Module Installer - Handles base module installation process

This module provides the BaseModuleInstaller class for installing the base module
using modern component-based approach with clean separation of concerns.
"""
from typing import TYPE_CHECKING

from ...logging import get_logger
from .installation_context import InstallationContext, InstallationUtilities

if TYPE_CHECKING:
    from ...environment import Environment


class BaseModuleInstaller:
    """
    Modern base module installer with clean separation of concerns.

    This installer handles base module installation without backward compatibility
    or legacy patterns. It uses dedicated components for each responsibility:
    - BaseSchemaManager: Database schema operations
    - IRPopulationManager: Shared IR metadata population (same as regular modules)
    - BaseModuleRegistrar: Module registration
    - Shared RegistryUpdater: Registry management (reusable utility)
    - Shared ValidationManager: Installation validation (reusable utility)

    No hooks are used for base module installation to ensure clean bootstrap.
    """

    def __init__(self):
        self.logger = get_logger(__name__)
        self.utilities = InstallationUtilities()

        # Initialize component managers
        self._schema_manager = None
        self._module_registrar = None

    def _get_schema_manager(self):
        """Lazy initialization of schema manager"""
        if self._schema_manager is None:
            from addons.base.schema_manager import BaseSchemaManager
            self._schema_manager = BaseSchemaManager()
        return self._schema_manager

    def _get_module_registrar(self):
        """Lazy initialization of module registrar"""
        if self._module_registrar is None:
            from addons.base.module_registrar import BaseModuleRegistrar
            self._module_registrar = BaseModuleRegistrar()
        return self._module_registrar

    async def install_base_module(self, env: 'Environment') -> bool:
        """
        Install the base module using modern component-based approach.

        This method orchestrates the installation process using dedicated
        components for each responsibility, ensuring clean separation of
        concerns and no backward compatibility dependencies.

        Args:
            env: Environment for database operations

        Returns:
            True if installation successful, False otherwise
        """
        context = InstallationContext('base', env, 'install')
        context.logger.info("🔧 Installing base module with modern component-based installer")

        try:
            # Step 1: Create database schema
            context.log_step("Step 1: Creating base database schema...")
            schema_manager = self._get_schema_manager()
            success = await schema_manager.create_base_schema(env.cr)
            if not success:
                context.logger.error("Failed to create base database schema")
                return False

            # Step 2: Populate IR metadata
            context.log_step("Step 2: Populating IR metadata...")
            from erp.utils import ir_population_manager

            # Use the shared IRPopulationManager for base module metadata population
            # Pass 'base' as addon_name to ensure base models are populated
            # For base module, we need to use a custom IR population approach
            # since the standard approach doesn't have the model mappings
            ir_results = await self._populate_base_ir_metadata(env.cr)

            if ir_results.get('status') == 'error':
                context.logger.error(f"Failed to populate IR metadata: {ir_results.get('message')}")
                return False

            # Step 3: Register base module
            context.log_step("Step 3: Registering base module...")
            module_registrar = self._get_module_registrar()
            success = await module_registrar.register_base_module(env.cr)
            if not success:
                context.logger.error("Failed to register base module")
                return False

            # Step 4: Update registry
            context.log_step("Step 4: Registry update handled by environment...")

            # Step 5: Validate installation
            context.log_step("Step 5: Validating installation...")

            # Use centralized lifecycle manager for temporary ModelRegistry
            context.log_step("Creating temporary ModelRegistry for base addon...")
            lifecycle_manager = self.utilities.get_lifecycle_manager()

            async with lifecycle_manager.temporary_registry_context('base') as model_registry:
                # Extract base module requirements from ModelRegistry
                base_tables = self.utilities.registry_extractor.extract_tables_from_registry(model_registry)
                base_models = self.utilities.registry_extractor.extract_models_from_registry(model_registry)
                base_constraints = self.utilities.registry_extractor.extract_constraints_from_registry(model_registry)
                base_indexes = self.utilities.registry_extractor.extract_indexes_from_registry(model_registry)

                context.logger.debug(f"Extracted from ModelRegistry - Tables: {len(base_tables)}, "
                                   f"Models: {len(base_models)}, Constraints: {len(base_constraints)}, "
                                   f"Indexes: {len(base_indexes)}")

                # Use shared validation manager with dynamically extracted requirements
                validation_manager = self.utilities.get_validation_manager()
                success = (
                    await validation_manager.validate_tables_exist(env.cr, base_tables) and
                    await validation_manager.validate_constraints_exist(env.cr, base_constraints) and
                    await validation_manager.validate_indexes_exist(env.cr, base_indexes) and
                    await validation_manager.validate_models_in_ir_metadata(env.cr, base_models) and
                    await validation_manager.validate_module_registration(env.cr, 'base')
                )
            
            if not success:
                context.logger.error("Installation validation failed")
                return False

            context.log_success()
            return True

        except Exception as e:
            context.log_error(e)
            return False

    async def get_installation_report(self, env: 'Environment') -> dict:
        """
        Get detailed installation report for debugging purposes.

        Args:
            env: Environment for database operations

        Returns:
            Dictionary containing detailed validation report
        """
        try:
            # Use centralized lifecycle manager for temporary ModelRegistry
            lifecycle_manager = self.utilities.get_lifecycle_manager()

            async with lifecycle_manager.temporary_registry_context('base') as model_registry:
                # Extract base module requirements from ModelRegistry
                base_tables = self.utilities.registry_extractor.extract_tables_from_registry(model_registry)
                base_models = self.utilities.registry_extractor.extract_models_from_registry(model_registry)
                base_constraints = self.utilities.registry_extractor.extract_constraints_from_registry(model_registry)
                base_indexes = self.utilities.registry_extractor.extract_indexes_from_registry(model_registry)

                # Use shared validation manager with dynamically extracted requirements
                validation_manager = self.utilities.get_validation_manager()
                return await validation_manager.get_validation_report(
                    env.cr, env, 'base', base_tables, base_models, base_constraints, base_indexes
                )
        except Exception as e:
            self.logger.error(f"Failed to generate installation report: {e}")
            return {'error': str(e), 'overall_valid': False}

    async def _populate_base_ir_metadata(self, db_manager: 'DatabaseManager') -> dict:
        """
        Populate IR metadata for base models using discovered models from ModelRegistry.
        This is a specialized version for base module installation.
        """
        try:
            from erp.utils.schema import SchemaGenerator
            from addons.base.schema_manager import BaseSchemaManager
            import uuid
            from datetime import datetime

            # Get discovered base models from BaseSchemaManager
            schema_manager = BaseSchemaManager()
            discovered_models = schema_manager.get_discovered_models()

            if not discovered_models:
                self.logger.error("No base models discovered for IR metadata population")
                return {'models_processed': 0, 'fields_processed': 0, 'success': False}

            models_processed = 0
            fields_processed = 0

            for model_name, model_class in discovered_models.items():
                try:
                    # Get model schema using the model class directly
                    model_schema = SchemaGenerator._extract_schema_from_model_class(model_class)

                    if not model_schema:
                        self.logger.error(f"Could not get schema for base model: {model_name}")
                        continue

                    # Register model in ir.model table
                    await self._register_base_model_metadata(db_manager, model_name, model_schema)
                    models_processed += 1

                    # Register model fields in ir.model.fields table
                    fields_count = await self._register_base_model_fields_metadata(db_manager, model_name, model_schema)
                    fields_processed += fields_count

                    self.logger.debug(f"✓ Registered base model {model_name} with {fields_count} fields")

                except Exception as e:
                    self.logger.error(f"Failed to register base model {model_name}: {e}")
                    continue

            return {
                'status': 'success',
                'models_processed': models_processed,
                'fields_processed': fields_processed
            }

        except Exception as e:
            self.logger.error(f"Failed to populate base IR metadata: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

    async def _register_base_model_metadata(self, db_manager: 'DatabaseManager', model_name: str, model_schema: dict) -> bool:
        """Register a base model in ir.model table"""
        try:
            import uuid
            from datetime import datetime

            # Check if model already exists
            exists = await db_manager.fetchval(
                "SELECT EXISTS(SELECT 1 FROM ir_model WHERE model = $1)",
                model_name
            )

            if exists:
                self.logger.debug(f"Base model {model_name} already exists in ir.model, skipping")
                return True

            # Insert model metadata
            model_id = str(uuid.uuid4())
            now = datetime.now()
            table_name = model_name.replace('.', '_')

            await db_manager.execute("""
                INSERT INTO ir_model (id, name, model, info, description, table_name_db, state, transient, create_at, update_at)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            """,
                model_id,
                model_name,  # name
                model_name,  # model
                f"Base model for {model_name}",  # info
                f"Base system model for {model_name}",  # description
                table_name,  # table_name_db
                'base',  # state
                False,  # transient
                now,  # create_at
                now   # update_at
            )

            self.logger.debug(f"✓ Registered base model metadata for {model_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to register base model {model_name}: {e}")
            return False

    async def _register_base_model_fields_metadata(self, db_manager: 'DatabaseManager', model_name: str, model_schema: dict) -> int:
        """Register base model fields in ir.model.fields table"""
        try:
            import uuid
            from datetime import datetime

            # First, get the model_id for this model from ir_model table
            model_id = await db_manager.fetchval(
                "SELECT id FROM ir_model WHERE model = $1",
                model_name
            )

            if not model_id:
                self.logger.error(f"Model {model_name} not found in ir_model table. Cannot register fields.")
                return 0

            fields_registered = 0

            for field_name, field_info in model_schema.get('fields', {}).items():
                try:
                    # Check if field already exists
                    exists = await db_manager.fetchval(
                        "SELECT EXISTS(SELECT 1 FROM ir_model_fields WHERE model = $1 AND name = $2)",
                        model_name, field_name
                    )

                    if exists:
                        continue

                    # Insert field metadata
                    field_id = str(uuid.uuid4())
                    now = datetime.now()

                    await db_manager.execute("""
                        INSERT INTO ir_model_fields (
                            id, model, name, field_description, ttype, relation, required,
                            readonly, translate, state, create_at, update_at, model_id
                        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
                    """,
                        field_id,
                        model_name,
                        field_name,
                        field_info.get('string', field_name),
                        field_info.get('type', 'char'),
                        field_info.get('relation'),
                        field_info.get('required', False),
                        field_info.get('readonly', False),
                        field_info.get('translate', False),
                        'base',  # state
                        now,  # create_at
                        now,   # update_at
                        model_id  # model_id - the missing required field
                    )

                    fields_registered += 1

                except Exception as e:
                    self.logger.error(f"Failed to register field {field_name} for model {model_name}: {e}")
                    continue

            return fields_registered

        except Exception as e:
            self.logger.error(f"Failed to register fields for base model {model_name}: {e}")
            return 0
